"""
语音评测API客户端模块

提供讯飞语音评测API的封装和调用功能
"""

import json
import time
import requests
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from PyQt6.QtCore import QObject, pyqtSignal

from utils.crypto_utils import XunfeiSigner, CryptoUtils
from utils.config_manager import ConfigManager
from utils.constants import TestType, Language
from utils.exception_handler import log_info, log_warning, log_error


class APIClient(QObject):
    """语音评测API客户端
    
    封装讯飞语音评测API的调用功能
    """
    
    # API调用信号
    request_started = pyqtSignal(str)  # 请求ID
    request_progress = pyqtSignal(str, int)  # 请求ID, 进度百分比
    request_completed = pyqtSignal(str, bool, dict)  # 请求ID, 是否成功, 结果数据
    request_failed = pyqtSignal(str, str, int)  # 请求ID, 错误信息, 错误代码
    
    def __init__(self, config_manager: Config<PERSON><PERSON>ger, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.session = requests.Session()
        self.signer = None
        self._setup_session()
        self._init_signer()
    
    def _setup_session(self):
        """设置HTTP会话"""
        # 设置默认头部
        self.session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
            'User-Agent': 'PutonghuaTest/1.0'
        })
        
        # 设置超时
        timeout = self.config_manager.get('api.timeout', 30)
        self.session.timeout = timeout
    
    def _init_signer(self):
        """初始化API签名器"""
        app_id = self.config_manager.get('api.app_id', '')
        api_key = self.config_manager.get('api.api_key', '')
        
        if app_id and api_key:
            self.signer = XunfeiSigner(app_id, api_key)
            log_info(f"API签名器初始化成功: app_id={app_id}")
        else:
            log_warning("API配置不完整，无法初始化签名器")
    
    def update_config(self, app_id: str, api_key: str):
        """更新API配置
        
        Args:
            app_id: 应用ID
            api_key: API密钥
        """
        self.config_manager.set('api.app_id', app_id, save=False)
        self.config_manager.set('api.api_key', api_key, save=True)
        self._init_signer()
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试API连接
        
        Returns:
            (是否连接成功, 消息)
        """
        if not self.signer:
            return False, "API配置不完整"
        
        try:
            # 使用简单的参数测试连接
            test_params = {
                'test': 'connection'
            }
            
            signed_params = self.signer.generate_signature(test_params)
            
            # 这里只是测试签名生成，不实际发送请求
            log_info("API连接测试通过")
            return True, "API连接正常"
            
        except Exception as e:
            error_msg = f"API连接测试失败: {str(e)}"
            log_error(error_msg)
            return False, error_msg
    
    def evaluate_audio(self, audio_data: str, text_content: str, 
                      test_type: TestType, language: Language = Language.CHINESE,
                      request_id: Optional[str] = None) -> str:
        """评测音频
        
        Args:
            audio_data: Base64编码的音频数据
            text_content: 测试文本内容
            test_type: 测试类型
            language: 语言类型
            request_id: 请求ID（可选）
            
        Returns:
            请求ID
        """
        if not self.signer:
            error_msg = "API未配置，无法进行评测"
            log_error(error_msg)
            if request_id:
                self.request_failed.emit(request_id, error_msg, -1)
            return ""
        
        # 生成请求ID
        if not request_id:
            request_id = f"req_{int(time.time() * 1000)}"
        
        try:
            # 发送开始信号
            self.request_started.emit(request_id)
            log_info(f"开始语音评测请求: {request_id}")
            
            # 构建请求参数
            params = self._build_request_params(
                audio_data, text_content, test_type, language
            )
            
            # 生成签名
            signed_params = self.signer.generate_signature(params)
            
            # 发送请求
            self._send_request(request_id, signed_params)
            
            return request_id
            
        except Exception as e:
            error_msg = f"评测请求失败: {str(e)}"
            log_error(error_msg)
            self.request_failed.emit(request_id, error_msg, -1)
            return ""
    
    def _build_request_params(self, audio_data: str, text_content: str,
                            test_type: TestType, language: Language) -> Dict[str, Any]:
        """构建请求参数
        
        Args:
            audio_data: 音频数据
            text_content: 文本内容
            test_type: 测试类型
            language: 语言类型
            
        Returns:
            请求参数字典
        """
        # 基础参数
        params = {
            'audio': audio_data,
            'text': text_content,
            'language': language.value,
            'category': self._get_category_by_test_type(test_type),
            'extra_ability': 'multi_dimension',  # 启用多维度评测
            'audio_format': 'wav',
            'sample_rate': '16000',
            'channel': '1',
            'bit_depth': '16'
        }
        
        # 根据测试类型添加特定参数
        if test_type == TestType.READ_SYLLABLE:
            params.update({
                'plev': '0',  # 难度等级
                'rank': '1'   # 打分等级
            })
        elif test_type == TestType.READ_WORD:
            params.update({
                'plev': '0',
                'rank': '1'
            })
        elif test_type == TestType.READ_SENTENCE:
            params.update({
                'plev': '0',
                'rank': '1'
            })
        elif test_type == TestType.READ_CHAPTER:
            params.update({
                'plev': '0',
                'rank': '1'
            })
        
        return params
    
    def _get_category_by_test_type(self, test_type: TestType) -> str:
        """根据测试类型获取API分类参数
        
        Args:
            test_type: 测试类型
            
        Returns:
            API分类参数
        """
        mapping = {
            TestType.READ_SYLLABLE: 'read_syllable',
            TestType.READ_WORD: 'read_word', 
            TestType.READ_SENTENCE: 'read_sentence',
            TestType.READ_CHAPTER: 'read_chapter'
        }
        return mapping.get(test_type, 'read_sentence')
    
    def _send_request(self, request_id: str, params: Dict[str, Any]):
        """发送API请求
        
        Args:
            request_id: 请求ID
            params: 请求参数
        """
        try:
            # 获取API URL
            api_url = self.config_manager.get('api.base_url', '')
            if not api_url:
                raise ValueError("API URL未配置")
            
            # 更新进度
            self.request_progress.emit(request_id, 20)
            
            # 发送POST请求
            response = self.session.post(
                api_url,
                data=params,
                timeout=self.config_manager.get('api.timeout', 30)
            )
            
            # 更新进度
            self.request_progress.emit(request_id, 60)
            
            # 检查HTTP状态码
            if response.status_code != 200:
                raise requests.RequestException(
                    f"HTTP错误: {response.status_code} - {response.text}"
                )
            
            # 解析响应
            result = self._parse_response(response.text)
            
            # 更新进度
            self.request_progress.emit(request_id, 100)
            
            # 发送完成信号
            self.request_completed.emit(request_id, True, result)
            log_info(f"API请求成功完成: {request_id}")
            
        except requests.Timeout:
            error_msg = "请求超时"
            log_error(f"API请求超时: {request_id}")
            self.request_failed.emit(request_id, error_msg, -2)
            
        except requests.ConnectionError:
            error_msg = "网络连接错误"
            log_error(f"API网络连接错误: {request_id}")
            self.request_failed.emit(request_id, error_msg, -3)
            
        except Exception as e:
            error_msg = f"请求处理失败: {str(e)}"
            log_error(f"API请求处理失败: {request_id} - {error_msg}")
            self.request_failed.emit(request_id, error_msg, -4)
    
    def _parse_response(self, response_text: str) -> Dict[str, Any]:
        """解析API响应
        
        Args:
            response_text: 响应文本
            
        Returns:
            解析后的响应数据
        """
        try:
            response_data = json.loads(response_text)
            
            # 检查响应状态
            code = response_data.get('code', -1)
            if code != 0:
                desc = response_data.get('desc', '未知错误')
                raise ValueError(f"API返回错误: {code} - {desc}")
            
            return response_data
            
        except json.JSONDecodeError as e:
            raise ValueError(f"响应JSON解析失败: {str(e)}")
    
    def cancel_request(self, request_id: str):
        """取消API请求
        
        Args:
            request_id: 请求ID
        """
        # 注意：这里只是示例，实际的取消逻辑需要根据具体的API实现
        log_info(f"取消API请求: {request_id}")
    
    def get_request_status(self, request_id: str) -> Optional[str]:
        """获取请求状态
        
        Args:
            request_id: 请求ID
            
        Returns:
            请求状态
        """
        # 这里可以实现请求状态查询逻辑
        return None
    
    def cleanup(self):
        """清理资源"""
        if self.session:
            self.session.close()
        log_info("API客户端资源已清理")


class APIError(Exception):
    """API错误异常类"""
    
    def __init__(self, message: str, code: int = -1, response_data: Dict = None):
        super().__init__(message)
        self.code = code
        self.response_data = response_data or {}
    
    def __str__(self):
        return f"APIError({self.code}): {super().__str__()}"


class APIRetryManager:
    """API重试管理器"""
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 1.0):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
    
    def should_retry(self, attempt: int, error: Exception) -> bool:
        """判断是否应该重试
        
        Args:
            attempt: 当前尝试次数
            error: 错误信息
            
        Returns:
            是否应该重试
        """
        if attempt >= self.max_retries:
            return False
        
        # 网络错误和超时错误可以重试
        if isinstance(error, (requests.ConnectionError, requests.Timeout)):
            return True
        
        # API错误根据错误码判断
        if isinstance(error, APIError):
            # 某些错误码可以重试（如服务器繁忙）
            retryable_codes = [10005, 10006, 10007]  # 示例错误码
            return error.code in retryable_codes
        
        return False
    
    def get_retry_delay(self, attempt: int) -> float:
        """获取重试延迟时间
        
        Args:
            attempt: 当前尝试次数
            
        Returns:
            延迟时间（秒）
        """
        # 指数退避策略
        return self.retry_delay * (2 ** attempt)
