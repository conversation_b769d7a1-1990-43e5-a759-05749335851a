2025-08-03 13:10:46,067 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:10:52,285 - PutonghuaTest - ERROR - 未捕获的异常: Traceback (most recent call last):
  File "G:\Code\Outsourcing\普通话测试\ui\main_window.py", line 354, in show_settings
    if show_settings_dialog(self.config_manager, self):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 417, in show_settings_dialog
    dialog = SettingsDialog(config_manager, parent)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 325, in __init__
    self.setup_ui()
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 343, in setup_ui
    self.general_settings = GeneralSettingsWidget(self.config_manager)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 180, in __init__
    super().__init__(parent)
  File "G:\Code\Outsourcing\普通话测试\ui\base_widget.py", line 23, in __init__
    self.setup_ui()
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 255, in setup_ui
    self.load_settings()
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 275, in load_settings
    self.font_size_spin.setValue(self.config_manager.get('ui.font_size', 12))
TypeError: setValue(self, val: int): argument 1 has unexpected type 'str'

2025-08-03 13:11:52,833 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 13:12:40,196 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:13:02,401 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:13:34,159 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:13:42,954 - PutonghuaTest - ERROR - 未捕获的异常: Traceback (most recent call last):
  File "G:\Code\Outsourcing\普通话测试\ui\main_window.py", line 296, in clear_results
    self.result_widget.clear_result()
  File "G:\Code\Outsourcing\普通话测试\ui\result_widget.py", line 526, in clear_result
    self.error_detail._show_no_error()
  File "G:\Code\Outsourcing\普通话测试\ui\result_widget.py", line 225, in _show_no_error
    self.detail_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'

2025-08-03 13:13:45,591 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 13:13:55,071 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:14:19,955 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:19:20,682 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:19:23,008 - PutonghuaTest - INFO - 音频处理完成
2025-08-03 13:19:27,739 - PutonghuaTest - INFO - 音频处理完成
2025-08-03 13:19:32,267 - PutonghuaTest - INFO - 音频处理完成
2025-08-03 13:19:34,515 - PutonghuaTest - INFO - 音频处理完成
2025-08-03 13:19:37,327 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:19:37,327 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:19:37,327 - PutonghuaTest - ERROR - 音频处理失败: name 'Path' is not defined
2025-08-03 13:19:37,329 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:19:39,346 - PutonghuaTest - INFO - 正在停止API工作线程...
2025-08-03 13:19:39,346 - PutonghuaTest - INFO - API工作线程已停止
2025-08-03 13:19:40,259 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 13:19:50,397 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:19:56,686 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
