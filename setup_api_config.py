#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API配置设置脚本

快速配置讯飞语音评测API参数
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config_manager import ConfigManager


def setup_api_config():
    """设置API配置"""
    print("正在配置讯飞语音评测API参数...")
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 设置API参数
        api_config = {
            'api.app_id': 'c94ba137',
            'api.api_key': '98cf67ac3d06e558bd1ab6db7438acd6',
            'api.api_secret': 'OTg5ZGU2MjcxNDVkNjRkOTNiMmYyN2U2',
            'api.base_url': 'https://api.xfyun.cn/v1/service/v1/ise',
            'api.timeout': 30
        }
        
        # 批量设置配置
        for key, value in api_config.items():
            config_manager.set(key, value, save=False)
        
        # 保存配置
        config_manager.save_config()
        
        print("✓ API配置设置成功！")
        print("\n配置详情:")
        print(f"  应用ID: {api_config['api.app_id']}")
        print(f"  API密钥: {api_config['api.api_key'][:8]}...")
        print(f"  API密钥: {api_config['api.api_secret'][:8]}...")
        print(f"  API地址: {api_config['api.base_url']}")
        print(f"  超时时间: {api_config['api.timeout']}秒")
        
        print("\n现在您可以运行以下命令启动应用程序:")
        print("python main.py")
        
        return True
        
    except Exception as e:
        print(f"✗ API配置设置失败: {str(e)}")
        return False


def test_api_config():
    """测试API配置"""
    print("\n正在测试API配置...")
    
    try:
        from core.api_client import APIClient
        from utils.config_manager import ConfigManager
        
        # 创建配置管理器和API客户端
        config_manager = ConfigManager()
        api_client = APIClient(config_manager)
        
        # 测试连接
        success, message = api_client.test_connection()
        
        if success:
            print("✓ API连接测试成功！")
            print(f"  {message}")
        else:
            print("✗ API连接测试失败")
            print(f"  {message}")
        
        return success
        
    except Exception as e:
        print(f"✗ API连接测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("讯飞语音评测API配置工具")
    print("=" * 50)
    
    # 设置API配置
    if not setup_api_config():
        return 1
    
    # 测试API配置
    if not test_api_config():
        print("\n⚠ 警告: API配置已保存，但连接测试失败")
        print("请检查网络连接和API参数是否正确")
        return 1
    
    print("\n🎉 配置完成！您现在可以使用普通话测试软件了。")
    return 0


if __name__ == "__main__":
    sys.exit(main())
