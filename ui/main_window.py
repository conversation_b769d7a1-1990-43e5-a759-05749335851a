"""
主窗口模块

应用程序的主窗口实现
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QStackedWidget, QMenuBar, QMenu, QStatusBar,
    QMessageBox, QApplication, QComboBox, QLabel
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QIcon, QKeySequence

from .base_widget import (
    BaseWidget, SectionWidget, ToolBarWidget,
    StatusBarWidget, PanelWidget, PlaceholderWidget
)
from .upload_widget import FileUploadWidget
from .result_widget import ResultDisplayWidget
from .settings_dialog import SettingsDialog, show_settings_dialog
from core.api_worker import APIWorker
from core.audio_processor import AudioProcessorWorker
from utils.config_manager import ConfigManager
from utils.constants import TestType, APP_NAME, Language
from utils.exception_handler import log_info, log_error, setup_exception_handler


class MainWindow(QMainWindow):
    """主窗口类
    
    应用程序的主窗口，包含所有主要的UI组件和布局
    """
    
    # 窗口信号
    window_closing = pyqtSignal()
    test_mode_changed = pyqtSignal(str)  # 测试模式变更
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.current_test_mode = TestType.READ_SYLLABLE.value

        # 初始化工作线程
        self.api_worker = None
        self.audio_worker = None

        # 初始化UI
        self.setup_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        self.load_window_settings()

        # 设置窗口属性
        self.setWindowTitle(APP_NAME)
        self.setMinimumSize(1000, 700)

        # 设置异常处理
        setup_exception_handler()
    
    def setup_ui(self):
        """设置主界面布局"""
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 工具栏
        self.toolbar_widget = ToolBarWidget()
        main_layout.addWidget(self.toolbar_widget)
        
        # 主分割器
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.main_splitter)
        
        # 左侧面板
        self.left_panel = self.create_left_panel()
        self.main_splitter.addWidget(self.left_panel)
        
        # 中央区域
        self.central_area = self.create_central_area()
        self.main_splitter.addWidget(self.central_area)

        # 设置分割器比例
        self.main_splitter.setSizes([350, 650])
        self.main_splitter.setCollapsible(0, True)
        
        # 状态栏
        self.status_widget = StatusBarWidget()
        main_layout.addWidget(self.status_widget)
    
    def create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = PanelWidget("文件上传", collapsible=True)

        # 文件上传组件
        self.upload_widget = FileUploadWidget()
        self.upload_widget.files_uploaded.connect(self.on_files_uploaded)
        self.upload_widget.audio_processed.connect(self.on_audio_processed)
        self.upload_widget.text_ready.connect(self.on_text_ready)
        panel.add_widget(self.upload_widget)

        return panel

    
    def create_central_area(self) -> QWidget:
        """创建中央区域"""
        # 使用堆叠组件支持多页面
        self.stacked_widget = QStackedWidget()

        # 欢迎页面
        welcome_page = self.create_welcome_page()
        self.stacked_widget.addWidget(welcome_page)

        # 结果展示页面
        self.result_widget = ResultDisplayWidget()
        self.result_widget.result_changed.connect(self.on_result_changed)
        self.stacked_widget.addWidget(self.result_widget)

        return self.stacked_widget
    
    def create_welcome_page(self) -> QWidget:
        """创建欢迎页面"""
        page = SectionWidget("欢迎使用普通话测试软件")

        welcome_text = PlaceholderWidget(
            "欢迎使用普通话测试软件！\n\n"
            "使用步骤：\n"
            "1. 在左侧上传音频文件和输入测试文本\n"
            "2. 选择测试类型（读字/读词语/读文章/自由说话）\n"
            "3. 点击开始评测按钮\n"
            "4. 查看评测结果和错误标注\n\n"
            "支持的音频格式：WAV, MP3, M4A, PCM\n"
            "建议音频参数：16kHz采样率，单声道，16bit位深度"
        )
        page.add_widget(welcome_text)

        return page
    

    

    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开文件
        open_action = QAction("打开音频文件(&O)", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.setStatusTip("打开音频文件进行测试")
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 导出结果
        export_action = QAction("导出结果(&E)", self)
        export_action.setStatusTip("导出测试结果")
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 设置菜单
        settings_menu = menubar.addMenu("设置(&S)")
        
        # 偏好设置
        preferences_action = QAction("偏好设置(&P)", self)
        preferences_action.setStatusTip("打开偏好设置")
        preferences_action.triggered.connect(self.show_settings)
        settings_menu.addAction(preferences_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于本软件")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """设置工具栏"""
        # 测试模式选择
        mode_label = QLabel("测试模式：")
        self.toolbar_widget.left_layout.addWidget(mode_label)
        
        self.mode_combo = QComboBox()
        self.mode_combo.addItem("读字测试", TestType.READ_SYLLABLE.value)
        self.mode_combo.addItem("读词语测试", TestType.READ_WORD.value)
        self.mode_combo.addItem("读文章测试", TestType.READ_SENTENCE.value)
        self.mode_combo.addItem("自由说话测试", TestType.READ_CHAPTER.value)
        self.mode_combo.currentTextChanged.connect(self.on_test_mode_changed)
        self.toolbar_widget.left_layout.addWidget(self.mode_combo)
        
        # 操作按钮
        self.start_button = self.toolbar_widget.add_button("start", "开始测试", "right")
        self.stop_button = self.toolbar_widget.add_button("stop", "停止测试", "right")
        self.clear_button = self.toolbar_widget.add_button("clear", "清除结果", "right")
        
        # 初始状态
        self.stop_button.setEnabled(False)
    
    def setup_statusbar(self):
        """设置状态栏"""
        self.status_widget.set_status("就绪")
    
    def setup_connections(self):
        """设置信号连接"""
        # 配置变更监听
        self.config_manager.config_changed.connect(self.on_config_changed)

        # 按钮连接
        self.start_button.clicked.connect(self.start_evaluation)
        self.stop_button.clicked.connect(self.stop_evaluation)
        self.clear_button.clicked.connect(self.clear_results)
    
    def on_test_mode_changed(self, text: str):
        """测试模式变更处理"""
        mode_data = self.mode_combo.currentData()
        if mode_data != self.current_test_mode:
            self.current_test_mode = mode_data
            self.test_mode_changed.emit(mode_data)
            self.status_widget.set_status(f"当前模式：{text}")
    
    def on_config_changed(self, key: str, value):
        """配置变更处理"""
        if key.startswith("ui.window"):
            # 窗口设置变更时更新界面
            pass
    
    def start_evaluation(self):
        """开始评测"""
        # 检查是否有上传的文件和文本
        uploaded_files = self.upload_widget.get_uploaded_files()
        current_text = self.upload_widget.get_current_text()

        if not uploaded_files:
            QMessageBox.warning(self, "警告", "请先上传音频文件")
            return

        if not current_text.strip():
            QMessageBox.warning(self, "警告", "请先输入测试文本")
            return

        # 更新UI状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_widget.set_status("正在进行语音评测...")

        # 初始化API工作线程
        self.api_worker = APIWorker(self.config_manager)
        self.api_worker.request_completed.connect(self.on_evaluation_completed)
        self.api_worker.request_failed.connect(self.on_evaluation_failed)
        self.api_worker.request_progress.connect(self.on_evaluation_progress)
        self.api_worker.start()

        # 获取当前测试类型
        test_type = TestType(self.current_test_mode)

        # 处理第一个音频文件
        first_file = uploaded_files[0]
        audio_path = first_file.get("path")

        if audio_path:
            # 处理音频文件
            self.process_audio_for_evaluation(audio_path, current_text, test_type)

    def stop_evaluation(self):
        """停止评测"""
        if self.api_worker and self.api_worker.isRunning():
            self.api_worker.stop()
            self.api_worker.wait()

        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_widget.set_status("评测已停止")

    def clear_results(self):
        """清除结果"""
        self.result_widget.clear_result()
        self.upload_widget.clear_all()
        self.stacked_widget.setCurrentIndex(0)
        self.status_widget.set_status("结果已清除")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            f"{APP_NAME}\n\n"
            "基于AI语音评测技术的普通话测试软件\n"
            "版本：1.0.0\n"
            "开发团队制作"
        )
    
    def load_window_settings(self):
        """加载窗口设置"""
        # 窗口大小
        width = self.config_manager.get('ui.window.width', 1200)
        height = self.config_manager.get('ui.window.height', 800)
        self.resize(width, height)
        
        # 窗口位置
        x = self.config_manager.get('ui.window.x', -1)
        y = self.config_manager.get('ui.window.y', -1)
        if x >= 0 and y >= 0:
            self.move(x, y)
        else:
            # 居中显示
            self.center_on_screen()
        
        # 最大化状态
        if self.config_manager.get('ui.window.maximized', False):
            self.showMaximized()
    
    def save_window_settings(self):
        """保存窗口设置"""
        if not self.isMaximized():
            self.config_manager.set('ui.window.width', self.width(), save=False)
            self.config_manager.set('ui.window.height', self.height(), save=False)
            self.config_manager.set('ui.window.x', self.x(), save=False)
            self.config_manager.set('ui.window.y', self.y(), save=False)
        
        self.config_manager.set('ui.window.maximized', self.isMaximized(), save=False)
        self.config_manager.save_config()
    
    def center_on_screen(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.geometry()
            x = (screen_geometry.width() - self.width()) // 2
            y = (screen_geometry.height() - self.height()) // 2
            self.move(x, y)
    
    def show_settings(self):
        """显示设置对话框"""
        if show_settings_dialog(self.config_manager, self):
            # 设置已更改，重新初始化API工作线程
            if self.api_worker:
                self.api_worker.stop()
                self.api_worker.wait()
                self.api_worker = None

            self.status_widget.set_status("设置已更新")

    def on_files_uploaded(self, files):
        """文件上传完成处理"""
        self.status_widget.set_status(f"已上传 {len(files)} 个文件")
        log_info(f"文件上传完成: {len(files)} 个文件")

    def on_audio_processed(self, result):
        """音频处理完成"""
        self.status_widget.set_status("音频处理完成")
        log_info("音频处理完成")

    def on_text_ready(self, text):
        """文本就绪"""
        self.status_widget.set_status("测试文本已准备")

    def on_result_changed(self, result):
        """结果变更处理"""
        self.status_widget.set_status("评测结果已更新")

    def process_audio_for_evaluation(self, audio_path, text_content, test_type):
        """处理音频进行评测"""
        try:
            # 创建音频处理工作线程
            self.audio_worker = AudioProcessorWorker(Path(audio_path))
            self.audio_worker.processing_finished.connect(
                lambda success, message, result: self.on_audio_ready_for_api(
                    success, message, result, text_content, test_type
                )
            )
            self.audio_worker.start()

        except Exception as e:
            log_error(f"音频处理失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"音频处理失败:\n{str(e)}")
            self.stop_evaluation()

    def on_audio_ready_for_api(self, success, message, result, text_content, test_type):
        """音频准备就绪，调用API"""
        if not success:
            QMessageBox.critical(self, "错误", f"音频处理失败:\n{message}")
            self.stop_evaluation()
            return

        try:
            # 获取Base64编码的音频数据
            audio_data = result.get("base64_data", "")
            if not audio_data:
                raise ValueError("音频数据为空")

            # 添加API请求到队列
            request_id = self.api_worker.add_request(
                audio_data=audio_data,
                text_content=text_content,
                test_type=test_type,
                language=Language.CHINESE
            )

            self.status_widget.set_status(f"正在调用API评测... (请求ID: {request_id[:8]})")

        except Exception as e:
            log_error(f"API调用失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"API调用失败:\n{str(e)}")
            self.stop_evaluation()

    def on_evaluation_progress(self, request_id, progress):
        """评测进度更新"""
        self.status_widget.set_status(f"评测进行中... {progress}%")

    def on_evaluation_completed(self, request_id, success, result):
        """评测完成"""
        if success:
            try:
                # 解析评测结果
                from core.result_parser import parse_api_response

                test_type = TestType(self.current_test_mode)
                text_content = self.upload_widget.get_current_text()

                evaluation_result = parse_api_response(
                    result, request_id, test_type, Language.CHINESE, text_content
                )

                # 显示结果
                self.result_widget.set_evaluation_result(evaluation_result)
                self.stacked_widget.setCurrentIndex(1)  # 切换到结果页面

                self.status_widget.set_status("评测完成")
                log_info(f"评测完成: {request_id}")

            except Exception as e:
                log_error(f"结果解析失败: {str(e)}")
                QMessageBox.critical(self, "错误", f"结果解析失败:\n{str(e)}")
        else:
            self.status_widget.set_status("评测失败")

        # 恢复UI状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def on_evaluation_failed(self, request_id, error_message, error_code):
        """评测失败"""
        log_error(f"评测失败: {request_id} - {error_message} (代码: {error_code})")
        QMessageBox.critical(self, "评测失败", f"评测失败:\n{error_message}\n错误代码: {error_code}")

        self.status_widget.set_status("评测失败")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止所有工作线程
        if self.api_worker and self.api_worker.isRunning():
            self.api_worker.stop()
            self.api_worker.wait()

        if self.audio_worker and self.audio_worker.isRunning():
            self.audio_worker.terminate()
            self.audio_worker.wait()

        # 保存窗口设置
        self.save_window_settings()

        # 发送关闭信号
        self.window_closing.emit()

        # 接受关闭事件
        event.accept()
