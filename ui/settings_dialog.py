"""
设置对话框模块

提供应用程序设置和配置管理界面
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QLineEdit, QPushButton, QCheckBox, QSpinBox,
    QComboBox, QTextEdit, QGroupBox, QFormLayout, QMessageBox,
    QFileDialog, QSlider, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread
from PyQt6.QtGui import QFont, QIcon

from .base_widget import BaseWidget
from core.api_client import APIClient
from utils.config_manager import ConfigManager
from utils.constants import TestType, Language
from utils.style_utils import StyleSheets, FontManager
from utils.exception_handler import log_info, log_error


class APITestWorker(QThread):
    """API测试工作线程"""
    
    test_completed = pyqtSignal(bool, str)  # 是否成功, 消息
    
    def __init__(self, config_manager: ConfigManager, app_id: str, api_key: str):
        super().__init__()
        self.config_manager = config_manager
        self.app_id = app_id
        self.api_key = api_key
    
    def run(self):
        """运行API测试"""
        try:
            # 临时更新配置
            original_app_id = self.config_manager.get('api.app_id')
            original_api_key = self.config_manager.get('api.api_key')
            
            self.config_manager.set('api.app_id', self.app_id, save=False)
            self.config_manager.set('api.api_key', self.api_key, save=False)
            
            # 创建API客户端并测试
            api_client = APIClient(self.config_manager)
            success, message = api_client.test_connection()
            
            # 恢复原始配置
            self.config_manager.set('api.app_id', original_app_id, save=False)
            self.config_manager.set('api.api_key', original_api_key, save=False)
            
            self.test_completed.emit(success, message)
            
        except Exception as e:
            self.test_completed.emit(False, f"测试失败: {str(e)}")


class APISettingsWidget(BaseWidget):
    """API设置组件"""
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        self.config_manager = config_manager
        super().__init__(parent)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # API配置组
        api_group = QGroupBox("讯飞API配置")
        api_layout = QFormLayout(api_group)
        
        # App ID
        self.app_id_edit = QLineEdit()
        self.app_id_edit.setPlaceholderText("请输入讯飞应用ID")
        api_layout.addRow("应用ID:", self.app_id_edit)
        
        # API Key
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setPlaceholderText("请输入API密钥")
        self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        api_layout.addRow("API密钥:", self.api_key_edit)
        
        # 显示密钥按钮
        show_key_layout = QHBoxLayout()
        self.show_key_checkbox = QCheckBox("显示密钥")
        self.show_key_checkbox.toggled.connect(self.toggle_key_visibility)
        show_key_layout.addWidget(self.show_key_checkbox)
        show_key_layout.addStretch()
        api_layout.addRow("", show_key_layout)
        
        # API URL
        self.api_url_edit = QLineEdit()
        self.api_url_edit.setPlaceholderText("https://api.xfyun.cn/v1/service/v1/ise")
        api_layout.addRow("API地址:", self.api_url_edit)
        
        # 超时设置
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 120)
        self.timeout_spin.setSuffix(" 秒")
        api_layout.addRow("请求超时:", self.timeout_spin)
        
        layout.addWidget(api_group)
        
        # 测试按钮
        test_layout = QHBoxLayout()
        self.test_button = QPushButton("测试连接")
        self.test_button.clicked.connect(self.test_api_connection)
        test_layout.addWidget(self.test_button)
        
        self.test_progress = QProgressBar()
        self.test_progress.setVisible(False)
        test_layout.addWidget(self.test_progress)
        
        test_layout.addStretch()
        layout.addLayout(test_layout)
        
        layout.addStretch()
        
        # 加载配置
        self.load_settings()
    
    def toggle_key_visibility(self, checked: bool):
        """切换密钥可见性"""
        if checked:
            self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
    
    def test_api_connection(self):
        """测试API连接"""
        app_id = self.app_id_edit.text().strip()
        api_key = self.api_key_edit.text().strip()
        
        if not app_id or not api_key:
            QMessageBox.warning(self, "警告", "请先填写应用ID和API密钥")
            return
        
        # 显示进度条
        self.test_progress.setVisible(True)
        self.test_progress.setRange(0, 0)  # 无限进度
        self.test_button.setEnabled(False)
        
        # 启动测试线程
        self.test_worker = APITestWorker(self.config_manager, app_id, api_key)
        self.test_worker.test_completed.connect(self.on_test_completed)
        self.test_worker.start()
    
    def on_test_completed(self, success: bool, message: str):
        """API测试完成"""
        self.test_progress.setVisible(False)
        self.test_button.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "成功", f"API连接测试成功!\n{message}")
        else:
            QMessageBox.warning(self, "失败", f"API连接测试失败:\n{message}")
    
    def load_settings(self):
        """加载设置"""
        self.app_id_edit.setText(self.config_manager.get('api.app_id', ''))
        self.api_key_edit.setText(self.config_manager.get('api.api_key', ''))
        self.api_url_edit.setText(self.config_manager.get('api.base_url', 'https://api.xfyun.cn/v1/service/v1/ise'))
        self.timeout_spin.setValue(self.config_manager.get('api.timeout', 30))
    
    def save_settings(self):
        """保存设置"""
        self.config_manager.set('api.app_id', self.app_id_edit.text().strip())
        self.config_manager.set('api.api_key', self.api_key_edit.text().strip())
        self.config_manager.set('api.base_url', self.api_url_edit.text().strip())
        self.config_manager.set('api.timeout', self.timeout_spin.value())


class GeneralSettingsWidget(BaseWidget):
    """通用设置组件"""
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        self.config_manager = config_manager
        super().__init__(parent)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 界面设置组
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout(ui_group)
        
        # 语言设置
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        ui_layout.addRow("界面语言:", self.language_combo)
        
        # 主题设置
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["浅色主题", "深色主题", "跟随系统"])
        ui_layout.addRow("界面主题:", self.theme_combo)
        
        # 字体大小
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setSuffix(" pt")
        ui_layout.addRow("字体大小:", self.font_size_spin)
        
        layout.addWidget(ui_group)
        
        # 文件设置组
        file_group = QGroupBox("文件设置")
        file_layout = QFormLayout(file_group)
        
        # 工作目录
        work_dir_layout = QHBoxLayout()
        self.work_dir_edit = QLineEdit()
        self.work_dir_edit.setReadOnly(True)
        work_dir_layout.addWidget(self.work_dir_edit)
        
        self.browse_dir_button = QPushButton("浏览")
        self.browse_dir_button.clicked.connect(self.browse_work_directory)
        work_dir_layout.addWidget(self.browse_dir_button)
        
        file_layout.addRow("工作目录:", work_dir_layout)
        
        # 自动保存
        self.auto_save_checkbox = QCheckBox("自动保存评测结果")
        file_layout.addRow("", self.auto_save_checkbox)
        
        # 保存格式
        self.save_format_combo = QComboBox()
        self.save_format_combo.addItems(["JSON", "HTML", "PDF"])
        file_layout.addRow("保存格式:", self.save_format_combo)
        
        layout.addWidget(file_group)
        
        # 性能设置组
        performance_group = QGroupBox("性能设置")
        performance_layout = QFormLayout(performance_group)
        
        # 并发请求数
        self.concurrent_requests_spin = QSpinBox()
        self.concurrent_requests_spin.setRange(1, 10)
        performance_layout.addRow("并发请求数:", self.concurrent_requests_spin)
        
        # 缓存大小
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(10, 1000)
        self.cache_size_spin.setSuffix(" MB")
        performance_layout.addRow("缓存大小:", self.cache_size_spin)
        
        layout.addWidget(performance_group)
        
        layout.addStretch()
        
        # 加载配置
        self.load_settings()
    
    def browse_work_directory(self):
        """浏览工作目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择工作目录", self.work_dir_edit.text()
        )
        if directory:
            self.work_dir_edit.setText(directory)
    
    def load_settings(self):
        """加载设置"""
        # 界面设置
        language = self.config_manager.get('ui.language', 'zh_CN')
        self.language_combo.setCurrentIndex(0 if language == 'zh_CN' else 1)
        
        theme = self.config_manager.get('ui.theme', 'light')
        theme_index = {'light': 0, 'dark': 1, 'auto': 2}.get(theme, 0)
        self.theme_combo.setCurrentIndex(theme_index)
        
        self.font_size_spin.setValue(self.config_manager.get('ui.font_size', 12))
        
        # 文件设置
        from utils.file_utils import get_work_directory
        work_dir = self.config_manager.get('file.work_directory', str(get_work_directory()))
        self.work_dir_edit.setText(work_dir)
        
        self.auto_save_checkbox.setChecked(self.config_manager.get('file.auto_save', True))
        
        save_format = self.config_manager.get('file.save_format', 'JSON')
        format_index = ['JSON', 'HTML', 'PDF'].index(save_format) if save_format in ['JSON', 'HTML', 'PDF'] else 0
        self.save_format_combo.setCurrentIndex(format_index)
        
        # 性能设置
        self.concurrent_requests_spin.setValue(self.config_manager.get('performance.concurrent_requests', 3))
        self.cache_size_spin.setValue(self.config_manager.get('performance.cache_size_mb', 100))
    
    def save_settings(self):
        """保存设置"""
        # 界面设置
        language = 'zh_CN' if self.language_combo.currentIndex() == 0 else 'en_US'
        self.config_manager.set('ui.language', language)
        
        theme_map = {0: 'light', 1: 'dark', 2: 'auto'}
        theme = theme_map.get(self.theme_combo.currentIndex(), 'light')
        self.config_manager.set('ui.theme', theme)
        
        self.config_manager.set('ui.font_size', self.font_size_spin.value())
        
        # 文件设置
        self.config_manager.set('file.work_directory', self.work_dir_edit.text())
        self.config_manager.set('file.auto_save', self.auto_save_checkbox.isChecked())
        
        save_format = self.save_format_combo.currentText()
        self.config_manager.set('file.save_format', save_format)
        
        # 性能设置
        self.config_manager.set('performance.concurrent_requests', self.concurrent_requests_spin.value())
        self.config_manager.set('performance.cache_size_mb', self.cache_size_spin.value())


class SettingsDialog(QDialog):
    """设置对话框"""
    
    # 设置变更信号
    settings_changed = pyqtSignal()
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.setup_ui()
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(600, 500)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # API设置选项卡
        self.api_settings = APISettingsWidget(self.config_manager)
        self.tab_widget.addTab(self.api_settings, "API设置")
        
        # 通用设置选项卡
        self.general_settings = GeneralSettingsWidget(self.config_manager)
        self.tab_widget.addTab(self.general_settings, "通用设置")
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        # 确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept_settings)
        self.ok_button.setDefault(True)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
    
    def reset_settings(self):
        """重置设置"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有设置到默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.config_manager.reset_to_defaults()
                self.api_settings.load_settings()
                self.general_settings.load_settings()
                QMessageBox.information(self, "成功", "设置已重置到默认值")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"重置设置失败:\n{str(e)}")
    
    def accept_settings(self):
        """接受设置"""
        try:
            # 保存所有设置
            self.api_settings.save_settings()
            self.general_settings.save_settings()
            
            # 保存配置文件
            self.config_manager.save_config()
            
            # 发送设置变更信号
            self.settings_changed.emit()
            
            # 关闭对话框
            self.accept()
            
            log_info("设置已保存")
            
        except Exception as e:
            log_error(f"保存设置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"保存设置失败:\n{str(e)}")


# 便捷函数
def show_settings_dialog(config_manager: ConfigManager, parent=None) -> bool:
    """显示设置对话框的便捷函数
    
    Returns:
        是否确认了设置更改
    """
    dialog = SettingsDialog(config_manager, parent)
    return dialog.exec() == QDialog.DialogCode.Accepted
